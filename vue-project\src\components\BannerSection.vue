<template>
  <section class="banner-section">
    <div class="banner-container">
      <div 
        v-for="(image, index) in bannerImages" 
        :key="index"
        class="banner-slide" 
        :class="{ active: currentSlide === index }"
      >
        <img :src="image.src" :alt="image.alt">
      </div>
      <div class="banner-nav">
        <span 
          v-for="(image, index) in bannerImages" 
          :key="index"
          class="banner-dot" 
          :class="{ active: currentSlide === index }"
          @click="goToSlide(index)"
        ></span>
      </div>
      <button class="banner-prev" @click="prevSlide">&#10094;</button>
      <button class="banner-next" @click="nextSlide">&#10095;</button>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const currentSlide = ref(0)
let autoSlideInterval = null

const bannerImages = [
  { src: '/banner/1-min.jpg', alt: '轮播图1' },
  { src: '/banner/22fc0e5c1aab426fae6abaf29308c580.jpg', alt: '轮播图2' },
  { src: '/banner/2bdd13fab41b42b987bcfc501aa535bb.jpg', alt: '轮播图3' }
]

const goToSlide = (index) => {
  currentSlide.value = index
  resetAutoSlide()
}

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % bannerImages.length
  resetAutoSlide()
}

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? bannerImages.length - 1 : currentSlide.value - 1
  resetAutoSlide()
}

const startAutoSlide = () => {
  autoSlideInterval = setInterval(() => {
    nextSlide()
  }, 5000)
}

const stopAutoSlide = () => {
  if (autoSlideInterval) {
    clearInterval(autoSlideInterval)
    autoSlideInterval = null
  }
}

const resetAutoSlide = () => {
  stopAutoSlide()
  startAutoSlide()
}

onMounted(() => {
  startAutoSlide()
})

onUnmounted(() => {
  stopAutoSlide()
})
</script>

<style scoped>
.banner-section {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.banner-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-slide {
  display: none;
  width: 100%;
  height: 100%;
}

.banner-slide.active {
  display: block;
}

.banner-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-nav {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.banner-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255,255,255,0.5);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.banner-dot.active {
  background-color: white;
}

.banner-prev,
.banner-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0,0,0,0.5);
  color: white;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  font-size: 18px;
  transition: background-color 0.3s ease;
}

.banner-prev {
  left: 20px;
}

.banner-next {
  right: 20px;
}

.banner-prev:hover,
.banner-next:hover {
  background-color: rgba(0,0,0,0.8);
}

@media (max-width: 768px) {
  .banner-section {
    height: 250px;
  }
  
  .banner-prev,
  .banner-next {
    padding: 10px 15px;
    font-size: 14px;
  }
}
</style>
