<script setup>
import { ref } from 'vue'
import TopBar from './components/TopBar.vue'
import NavBar from './components/NavBar.vue'
import BannerSection from './components/BannerSection.vue'
import ServicesSection from './components/ServicesSection.vue'
import ProcessSection from './components/ProcessSection.vue'
import AboutPage from './components/AboutPage.vue'
import ContactPage from './components/ContactPage.vue'
import FooterSection from './components/FooterSection.vue'

const currentPage = ref('home')

const showPage = (page) => {
  currentPage.value = page
}
</script>

<template>
  <div id="app">
    <!-- 顶部信息栏 -->
    <TopBar />

    <!-- 导航栏 -->
    <NavBar :currentPage="currentPage" @changePage="showPage" />

    <!-- 首页内容 -->
    <main v-if="currentPage === 'home'">
      <BannerSection />
      <ServicesSection />
      <ProcessSection />
    </main>

    <!-- 关于我们页面 -->
    <AboutPage v-if="currentPage === 'about'" />

    <!-- 联系我们页面 -->
    <ContactPage v-if="currentPage === 'contact'" />

    <!-- 页脚 -->
    <FooterSection />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
</style>
