<template>
  <section class="page-section">
    <div class="container">
      <h1>联系我们</h1>
      <div class="contact-content">
        <div class="contact-info">
          <div class="contact-item">
            <div class="contact-icon">📞</div>
            <h3>服务热线</h3>
            <p class="phone">4008326986</p>
          </div>
          <div class="contact-item">
            <div class="contact-icon">🏢</div>
            <h3>公司信息</h3>
            <p>安徽祚晟电子科技有限公司</p>
          </div>
          <div class="contact-item">
            <div class="contact-icon">🕒</div>
            <h3>服务时间</h3>
            <p>24小时全天候服务</p>
          </div>
          <div class="contact-item">
            <div class="contact-icon">📍</div>
            <h3>服务范围</h3>
            <p>全国各大城市</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.page-section {
  padding: 60px 0;
  min-height: 60vh;
}

.page-section h1 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 36px;
  color: #2c3e50;
}

.contact-content {
  max-width: 800px;
  margin: 0 auto;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.contact-item {
  text-align: center;
  padding: 30px;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.contact-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.contact-item h3 {
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 20px;
}

.contact-item p {
  font-size: 16px;
  color: #666;
  margin-bottom: 0;
}

.contact-item .phone {
  font-size: 24px;
  color: #3498db;
  font-weight: bold;
}

@media (max-width: 768px) {
  .page-section h1 {
    font-size: 28px;
  }
  
  .contact-info {
    grid-template-columns: 1fr;
  }
}
</style>
