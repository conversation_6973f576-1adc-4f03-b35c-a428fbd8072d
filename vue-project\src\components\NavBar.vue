<template>
  <nav class="navbar">
    <div class="container">
      <div class="nav-content">
        <div class="logo">
          <img src="/LOGO.jpg" alt="今师傅">
        </div>
        <ul class="nav-menu" :class="{ 'mobile-active': mobileMenuOpen }">
          <li>
            <a 
              href="#" 
              :class="{ active: currentPage === 'home' }" 
              @click.prevent="changePage('home')"
            >
              首页
            </a>
          </li>
          <li>
            <a 
              href="#" 
              :class="{ active: currentPage === 'about' }" 
              @click.prevent="changePage('about')"
            >
              关于我们
            </a>
          </li>
          <li>
            <a 
              href="#" 
              :class="{ active: currentPage === 'contact' }" 
              @click.prevent="changePage('contact')"
            >
              联系我们
            </a>
          </li>
        </ul>
        <button 
          class="mobile-menu-btn" 
          @click="toggleMobileMenu"
          v-if="isMobile"
        >
          ☰
        </button>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps(['currentPage'])
const emit = defineEmits(['changePage'])

const mobileMenuOpen = ref(false)
const isMobile = ref(false)

const changePage = (page) => {
  emit('changePage', page)
  mobileMenuOpen.value = false
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.navbar {
  background-color: white;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.logo img {
  height: 50px;
  width: auto;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  padding: 10px 15px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
  background-color: #3498db;
  color: white;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  color: #333;
  cursor: pointer;
  padding: 10px;
}

@media (max-width: 768px) {
  .nav-content {
    flex-direction: row;
    justify-content: space-between;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    flex-direction: column;
    gap: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: none;
  }
  
  .nav-menu.mobile-active {
    display: flex;
  }
  
  .nav-menu li {
    border-bottom: 1px solid #eee;
  }
  
  .nav-menu a {
    display: block;
    padding: 15px 20px;
    border-radius: 0;
  }
}
</style>
