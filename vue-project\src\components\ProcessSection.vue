<template>
  <section class="process-section">
    <div class="container">
      <h2>服务流程</h2>
      <div class="process-steps">
        <div 
          v-for="step in processSteps" 
          :key="step.number"
          class="step"
        >
          <div class="step-number">{{ step.number }}</div>
          <div class="step-content">
            <h3>{{ step.title }}</h3>
            <p>{{ step.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const processSteps = [
  {
    number: 1,
    title: '在线预约',
    description: '通过电话或在线平台预约服务'
  },
  {
    number: 2,
    title: '师傅上门',
    description: '专业师傅按时上门服务'
  },
  {
    number: 3,
    title: '问题诊断',
    description: '专业诊断问题并报价'
  },
  {
    number: 4,
    title: '维修完成',
    description: '维修完成并提供质保'
  }
]
</script>

<style scoped>
.process-section {
  padding: 60px 0;
  background-color: white;
}

.process-section h2 {
  text-align: center;
  margin-bottom: 50px;
  font-size: 32px;
  color: #2c3e50;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.step {
  text-align: center;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  margin: 0 auto 20px;
}

.step-content h3 {
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 20px;
}

.step-content p {
  color: #666;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .process-steps {
    grid-template-columns: 1fr;
  }
  
  .process-section h2 {
    font-size: 24px;
  }
}
</style>
