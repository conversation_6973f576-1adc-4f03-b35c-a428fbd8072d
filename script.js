// 轮播图功能
let slideIndex = 1;
let slideInterval;

// 初始化轮播图
document.addEventListener('DOMContentLoaded', function() {
    showSlides(slideIndex);
    startAutoSlide();
});

// 显示指定的幻灯片
function showSlides(n) {
    let slides = document.getElementsByClassName('banner-slide');
    let dots = document.getElementsByClassName('banner-dot');
    
    if (n > slides.length) { slideIndex = 1; }
    if (n < 1) { slideIndex = slides.length; }
    
    for (let i = 0; i < slides.length; i++) {
        slides[i].classList.remove('active');
    }
    
    for (let i = 0; i < dots.length; i++) {
        dots[i].classList.remove('active');
    }
    
    if (slides[slideIndex - 1]) {
        slides[slideIndex - 1].classList.add('active');
    }
    if (dots[slideIndex - 1]) {
        dots[slideIndex - 1].classList.add('active');
    }
}

// 切换到下一张/上一张
function plusSlides(n) {
    stopAutoSlide();
    slideIndex += n;
    showSlides(slideIndex);
    startAutoSlide();
}

// 切换到指定幻灯片
function currentSlide(n) {
    stopAutoSlide();
    slideIndex = n;
    showSlides(slideIndex);
    startAutoSlide();
}

// 开始自动轮播
function startAutoSlide() {
    slideInterval = setInterval(function() {
        slideIndex++;
        showSlides(slideIndex);
    }, 5000); // 每5秒切换一次
}

// 停止自动轮播
function stopAutoSlide() {
    if (slideInterval) {
        clearInterval(slideInterval);
    }
}

// 鼠标悬停时停止自动轮播
document.querySelector('.banner-container').addEventListener('mouseenter', stopAutoSlide);
document.querySelector('.banner-container').addEventListener('mouseleave', startAutoSlide);

// 服务分类切换功能
function showService(serviceType) {
    // 移除所有活动状态
    const tabBtns = document.querySelectorAll('.tab-btn');
    const servicePanels = document.querySelectorAll('.service-panel');
    
    tabBtns.forEach(btn => btn.classList.remove('active'));
    servicePanels.forEach(panel => panel.classList.remove('active'));
    
    // 添加活动状态到当前选中的标签和面板
    event.target.classList.add('active');
    document.getElementById(serviceType + '-service').classList.add('active');
}

// 导航功能
function showPage(pageId) {
    // 隐藏所有页面
    const pages = ['home', 'about', 'contact'];
    pages.forEach(page => {
        const element = document.getElementById(page);
        if (element) {
            if (page === 'home') {
                element.style.display = 'block';
            } else {
                element.style.display = 'none';
            }
        }
    });
    
    // 显示选中的页面
    if (pageId !== 'home') {
        document.getElementById('home').style.display = 'none';
        document.getElementById(pageId).style.display = 'block';
    }
    
    // 更新导航菜单活动状态
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => link.classList.remove('active'));
    document.querySelector(`a[href="#${pageId}"]`).classList.add('active');
}

// 导航点击事件
document.addEventListener('DOMContentLoaded', function() {
    const navLinks = document.querySelectorAll('.nav-menu a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const pageId = this.getAttribute('href').substring(1);
            showPage(pageId);
        });
    });
});

// 平滑滚动效果
function smoothScroll(target) {
    const element = document.querySelector(target);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 添加滚动动画效果
function addScrollAnimation() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // 观察需要动画的元素
    const animatedElements = document.querySelectorAll('.service-item, .step, .about-content, .contact-content');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// 页面加载完成后初始化动画
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(addScrollAnimation, 100);
});

// 响应式导航菜单（移动端）
function toggleMobileMenu() {
    const navMenu = document.querySelector('.nav-menu');
    navMenu.classList.toggle('mobile-active');
}

// 添加移动端菜单按钮（如果需要）
function createMobileMenuButton() {
    if (window.innerWidth <= 768) {
        const navContent = document.querySelector('.nav-content');
        let mobileBtn = document.querySelector('.mobile-menu-btn');
        
        if (!mobileBtn) {
            mobileBtn = document.createElement('button');
            mobileBtn.className = 'mobile-menu-btn';
            mobileBtn.innerHTML = '☰';
            mobileBtn.onclick = toggleMobileMenu;
            navContent.appendChild(mobileBtn);
        }
    }
}

// 窗口大小改变时检查是否需要移动端菜单
window.addEventListener('resize', createMobileMenuButton);
document.addEventListener('DOMContentLoaded', createMobileMenuButton);
