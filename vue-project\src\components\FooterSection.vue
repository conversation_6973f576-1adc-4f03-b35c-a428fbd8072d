<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>今师傅</h3>
          <p>专业的维修服务平台</p>
          <p>让维修更简单，让生活更美好</p>
        </div>

        <div class="footer-section">
          <h3>服务项目</h3>
          <ul>
            <li>家庭维修</li>
            <li>办公维修</li>
            <li>汽车维修</li>
            <li>24小时服务</li>
          </ul>
        </div>

        <div class="footer-section">
          <h3>法律信息</h3>
          <ul>
            <li><a href="#" @click.prevent="changePage('terms')">用户协议</a></li>
            <li><a href="#" @click.prevent="changePage('privacy')">隐私政策</a></li>
            <li>服务条款</li>
            <li>免责声明</li>
          </ul>
        </div>

        <div class="footer-section">
          <h3>联系我们</h3>
          <ul>
            <li>服务热线：4008326986</li>
        
           
            <li>工作时间：24小时全天候</li>
          </ul>
        </div>
      </div>

      <div class="footer-bottom">
        <p>&copy; 2023-2027 安徽祚晟电子科技有限公司 版权所有</p>
        <p>备案号：皖ICP备2023012035号 | 经营许可证：皖B2-20230001</p>
        <p>今师傅应用 - 专业维修服务平台 | 全国统一服务热线：4008326986</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
const emit = defineEmits(['changePage'])

const changePage = (page) => {
  emit('changePage', page)
}
</script>

<style scoped>
.footer {
  background-color: #2c3e50;
  color: white;
  padding: 40px 0 20px;
  margin-top: 50px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section h3 {
  color: #3498db;
  font-size: 18px;
  margin-bottom: 20px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.footer-section p {
  margin: 10px 0;
  color: #bdc3c7;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin: 8px 0;
  color: #bdc3c7;
  line-height: 1.6;
}

.footer-section a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: #3498db;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding-top: 20px;
  text-align: center;
}

.footer-bottom p {
  margin: 5px 0;
  font-size: 14px;
  color: #95a5a6;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-section {
    text-align: center;
  }
}
</style>
