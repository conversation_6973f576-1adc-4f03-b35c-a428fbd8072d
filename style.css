* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 顶部信息栏 */
.top-bar {
    background-color: #2c3e50;
    color: white;
    padding: 10px 0;
    font-size: 14px;
}

.contact-info {
    text-align: right;
}

/* 导航栏 */
.navbar {
    background-color: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo img {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 10px 15px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
    background-color: #3498db;
    color: white;
}

/* 轮播图 */
.banner-section {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.banner-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.banner-slide {
    display: none;
    width: 100%;
    height: 100%;
}

.banner-slide.active {
    display: block;
}

.banner-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-nav {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.banner-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.banner-dot.active {
    background-color: white;
}

.banner-prev,
.banner-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0,0,0,0.5);
    color: white;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    font-size: 18px;
    transition: background-color 0.3s ease;
}

.banner-prev {
    left: 20px;
}

.banner-next {
    right: 20px;
}

.banner-prev:hover,
.banner-next:hover {
    background-color: rgba(0,0,0,0.8);
}

/* 服务分类 */
.services-section {
    padding: 60px 0;
    background-color: #f8f9fa;
}

.services-section h2 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 32px;
    color: #2c3e50;
}

.service-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
}

.tab-btn {
    padding: 12px 30px;
    border: 2px solid #3498db;
    background-color: white;
    color: #3498db;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background-color: #3498db;
    color: white;
}

.service-content {
    position: relative;
}

.service-panel {
    display: none;
}

.service-panel.active {
    display: block;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-item {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.service-item:hover {
    transform: translateY(-5px);
}

.service-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.service-item h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 20px;
}

.service-item p {
    color: #666;
    line-height: 1.6;
}

/* 服务流程 */
.process-section {
    padding: 60px 0;
    background-color: white;
}

.process-section h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 32px;
    color: #2c3e50;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #3498db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    margin: 0 auto 20px;
}

.step-content h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 20px;
}

.step-content p {
    color: #666;
    line-height: 1.6;
}

/* 页面部分 */
.page-section {
    padding: 60px 0;
    min-height: 60vh;
}

.page-section h1 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 36px;
    color: #2c3e50;
}

.about-content,
.contact-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-content h2 {
    margin-bottom: 20px;
    color: #3498db;
    font-size: 28px;
}

.about-content p {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.8;
    color: #666;
}

.contact-content h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 24px;
}

.contact-content .phone {
    font-size: 28px;
    color: #3498db;
    font-weight: bold;
    margin-bottom: 30px;
}

.contact-content p {
    font-size: 16px;
    color: #666;
    margin-bottom: 15px;
}

/* 页脚 */
.footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: 50px;
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 24px;
    color: #333;
    cursor: pointer;
    padding: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-content {
        flex-direction: row;
        justify-content: space-between;
    }

    .mobile-menu-btn {
        display: block;
    }

    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        flex-direction: column;
        gap: 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: none;
    }

    .nav-menu.mobile-active {
        display: flex;
    }

    .nav-menu li {
        border-bottom: 1px solid #eee;
    }

    .nav-menu a {
        display: block;
        padding: 15px 20px;
        border-radius: 0;
    }

    .banner-section {
        height: 250px;
    }

    .service-tabs {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .tab-btn {
        width: 200px;
    }

    .process-steps {
        grid-template-columns: 1fr;
    }

    .banner-prev,
    .banner-next {
        padding: 10px 15px;
        font-size: 14px;
    }

    .services-section h2,
    .process-section h2 {
        font-size: 24px;
    }

    .page-section h1 {
        font-size: 28px;
    }
}
