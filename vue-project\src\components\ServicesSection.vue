<template>
  <section class="services-section">
    <div class="container">
      <h2>我们的服务</h2>
      <div class="service-tabs">
        <button 
          v-for="tab in serviceTabs" 
          :key="tab.id"
          class="tab-btn" 
          :class="{ active: activeTab === tab.id }"
          @click="setActiveTab(tab.id)"
        >
          {{ tab.name }}
        </button>
      </div>
      <div class="service-content">
        <div 
          v-for="tab in serviceTabs" 
          :key="tab.id"
          class="service-panel" 
          :class="{ active: activeTab === tab.id }"
        >
          <div class="service-grid">
            <div 
              v-for="service in tab.services" 
              :key="service.title"
              class="service-item"
            >
              <div class="service-icon">{{ service.icon }}</div>
              <h3>{{ service.title }}</h3>
              <p>{{ service.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

const activeTab = ref('home')

const serviceTabs = [
  {
    id: 'home',
    name: '家庭维修',
    services: [
      { icon: '🔧', title: '水电维修', description: '专业水电安装维修服务' },
      { icon: '🏠', title: '家具维修', description: '家具安装维修保养' },
      { icon: '❄️', title: '家电维修', description: '各类家电维修保养' },
      { icon: '🔨', title: '装修维修', description: '室内装修维修服务' }
    ]
  },
  {
    id: 'office',
    name: '办公维修',
    services: [
      { icon: '💻', title: '电脑维修', description: '办公电脑维修保养' },
      { icon: '🖨️', title: '打印机维修', description: '各类打印设备维修' },
      { icon: '📞', title: '通讯设备', description: '电话网络设备维修' },
      { icon: '🏢', title: '办公设施', description: '办公家具设施维修' }
    ]
  },
  {
    id: 'car',
    name: '汽车维修',
    services: [
      { icon: '🚗', title: '汽车保养', description: '定期保养维护服务' },
      { icon: '🔧', title: '故障维修', description: '各类故障诊断维修' },
      { icon: '🛞', title: '轮胎服务', description: '轮胎更换维修服务' },
      { icon: '🔋', title: '电瓶服务', description: '电瓶检测更换服务' }
    ]
  }
]

const setActiveTab = (tabId) => {
  activeTab.value = tabId
}
</script>

<style scoped>
.services-section {
  padding: 60px 0;
  background-color: #f8f9fa;
}

.services-section h2 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 32px;
  color: #2c3e50;
}

.service-tabs {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
}

.tab-btn {
  padding: 12px 30px;
  border: 2px solid #3498db;
  background-color: white;
  color: #3498db;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
  background-color: #3498db;
  color: white;
}

.service-content {
  position: relative;
}

.service-panel {
  display: none;
}

.service-panel.active {
  display: block;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.service-item {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.service-item:hover {
  transform: translateY(-5px);
}

.service-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.service-item h3 {
  margin-bottom: 15px;
  color: #2c3e50;
  font-size: 20px;
}

.service-item p {
  color: #666;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .service-tabs {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  .tab-btn {
    width: 200px;
  }
  
  .services-section h2 {
    font-size: 24px;
  }
}
</style>
