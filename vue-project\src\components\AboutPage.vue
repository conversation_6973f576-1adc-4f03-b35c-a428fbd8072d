<template>
  <section class="page-section">
    <div class="container">
      <h1>关于我们</h1>
      <div class="about-content">
        <div class="company-intro">
          <h2>今师傅 - 专业维修服务平台</h2>
          <p class="intro-text">今师傅是安徽祚晟电子科技有限公司旗下的专业维修服务平台，成立于2023年，致力于为广大用户提供优质、便捷、专业的维修服务。我们拥有经验丰富的师傅团队，覆盖家庭维修、办公维修、汽车维修等多个领域。</p>
          <p class="intro-text">我们秉承"专业、诚信、高效、创新"的服务理念，通过互联网技术与传统维修服务的深度融合，为每一位客户提供满意的服务体验，让维修更简单，让生活更美好。</p>
        </div>

        <div class="company-info">
          <h3>公司信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <strong>公司全称：</strong>安徽祚晟电子科技有限公司
            </div>
            <div class="info-item">
              <strong>成立时间：</strong>2023年
            </div>
            
            <div class="info-item">
              <strong>备案号：</strong>皖ICP备2023012035号
            </div>
            <div class="info-item">
              <strong>服务热线：</strong>4008326986
            </div>
            <div class="info-item">
              <strong>服务范围：</strong>全国主要城市
            </div>
          </div>
        </div>

        <div class="mission-vision">
          <div class="mission">
            <h3>我们的使命</h3>
            <p>通过专业的维修服务和先进的技术平台，为用户提供便捷、高效、可靠的维修解决方案，提升生活品质。</p>
          </div>
          <div class="vision">
            <h3>我们的愿景</h3>
            <p>成为中国领先的维修服务平台，让每个家庭都能享受到专业、便捷的维修服务。</p>
          </div>
        </div>

        <div class="features">
          <h3>服务特色</h3>
          <div class="feature-grid">
            <div class="feature-item">
              <div class="feature-icon">👨‍🔧</div>
              <h4>专业团队</h4>
              <p>经验丰富的专业师傅，经过严格筛选和培训认证</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">⚡</div>
              <h4>快速响应</h4>
              <p>24小时在线客服，快速响应用户需求</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🛡️</div>
              <h4>质量保证</h4>
              <p>提供完善的质保服务，让用户使用更安心</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">💰</div>
              <h4>价格透明</h4>
              <p>明码标价，无隐藏费用，让用户消费更放心</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📱</div>
              <h4>便捷预约</h4>
              <p>支持在线预约，随时随地享受维修服务</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🏆</div>
              <h4>服务保障</h4>
              <p>完善的服务流程和质量监控体系</p>
            </div>
          </div>
        </div>

        <div class="development-history">
          <h3>发展历程</h3>
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-date">2023年</div>
              <div class="timeline-content">
                <h4>公司成立</h4>
                <p>安徽祚晟电子科技有限公司正式成立，今师傅平台开始筹备</p>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">2023年下半年</div>
              <div class="timeline-content">
                <h4>平台上线</h4>
                <p>今师傅维修服务平台正式上线，开始为用户提供专业维修服务</p>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">未来规划</div>
              <div class="timeline-content">
                <h4>持续发展</h4>
                <p>不断完善服务体系，扩大服务范围，提升用户体验</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.page-section {
  padding: 60px 0;
  min-height: 80vh;
  background-color: #f8f9fa;
}

.page-section h1 {
  text-align: center;
  margin-bottom: 50px;
  font-size: 36px;
  color: #2c3e50;
}

.about-content {
  max-width: 1000px;
  margin: 0 auto;
}

.company-intro {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  margin-bottom: 40px;
  text-align: center;
}

.company-intro h2 {
  margin-bottom: 30px;
  color: #3498db;
  font-size: 32px;
}

.intro-text {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  text-align: left;
}

.company-info {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  margin-bottom: 40px;
}

.company-info h3 {
  color: #2c3e50;
  font-size: 24px;
  margin-bottom: 25px;
  text-align: center;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.info-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #2c3e50;
}

.mission-vision {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.mission, .vision {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  text-align: center;
}

.mission h3, .vision h3 {
  color: #3498db;
  font-size: 22px;
  margin-bottom: 20px;
}

.mission p, .vision p {
  color: #666;
  line-height: 1.8;
}

.features {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  margin-bottom: 40px;
}

.features h3 {
  color: #2c3e50;
  font-size: 24px;
  margin-bottom: 30px;
  text-align: center;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.feature-item {
  padding: 25px;
  border-radius: 10px;
  background-color: #f8f9fa;
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.feature-item h4 {
  margin-bottom: 10px;
  color: #2c3e50;
  font-size: 18px;
}

.feature-item p {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.development-history {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.development-history h3 {
  color: #2c3e50;
  font-size: 24px;
  margin-bottom: 30px;
  text-align: center;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #3498db;
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
  padding-left: 40px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #3498db;
}

.timeline-date {
  color: #3498db;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
}

.timeline-content h4 {
  color: #2c3e50;
  font-size: 18px;
  margin-bottom: 8px;
}

.timeline-content p {
  color: #666;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .page-section h1 {
    font-size: 28px;
  }

  .company-intro, .company-info, .features, .development-history {
    padding: 20px;
    margin: 0 10px 30px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .mission-vision {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
