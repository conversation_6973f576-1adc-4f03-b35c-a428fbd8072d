<template>
  <section class="page-section">
    <div class="container">
      <h1>关于我们</h1>
      <div class="about-content">
        <h2>今师傅</h2>
        <p>今师傅是一家专业的社区便民服务平台，致力于为广大用户提供优质、便捷的维修服务。我们拥有专业的师傅团队，覆盖家庭维修、办公维修、汽车维修等多个领域。</p>
        <p>我们秉承"专业、诚信、高效"的服务理念，为每一位客户提供满意的服务体验。</p>
        <div class="features">
          <div class="feature-item">
            <div class="feature-icon">👨‍🔧</div>
            <h3>专业团队</h3>
            <p>经验丰富的专业师傅</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">⚡</div>
            <h3>快速响应</h3>
            <p>24小时快速响应服务</p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🛡️</div>
            <h3>质量保证</h3>
            <p>提供完善的质保服务</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.page-section {
  padding: 60px 0;
  min-height: 60vh;
}

.page-section h1 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 36px;
  color: #2c3e50;
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-content h2 {
  margin-bottom: 20px;
  color: #3498db;
  font-size: 28px;
}

.about-content p {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.8;
  color: #666;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.feature-item {
  padding: 20px;
  border-radius: 10px;
  background-color: #f8f9fa;
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.feature-item h3 {
  margin-bottom: 10px;
  color: #2c3e50;
  font-size: 18px;
}

.feature-item p {
  color: #666;
  font-size: 14px;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .page-section h1 {
    font-size: 28px;
  }
  
  .features {
    grid-template-columns: 1fr;
  }
}
</style>
